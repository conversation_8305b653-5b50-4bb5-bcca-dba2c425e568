<template>
  <ZButton :class="['code-countdown-btn', { 'counting': isCounting, 'disabled': disabled }]"
    :disabled="disabled || isCounting" @click="handleClick">
    {{ buttonText }}
  </ZButton>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';

interface Props {
  /** 倒计时时长（秒），默认60秒 */
  duration?: number;
  /** 存储键名，用于区分不同的倒计时 */
  storageKey?: string;
  /** 是否禁用按钮 */
  disabled?: boolean;
  /** 未点击时的按钮文本 */
  defaultText?: string;
  /** 倒计时结束后的按钮文本 */
  finishedText?: string;
}

interface Emits {
  /** 点击按钮时触发 */
  (e: 'click'): void;
  /** 倒计时状态变化时触发 */
  (e: 'countdown-change', isCounting: boolean, remainingTime: number): void;
}

const props = withDefaults(defineProps<Props>(), {
  duration: 60,
  storageKey: 'code_countdown',
  disabled: false,
  defaultText: 'Get Code',
  finishedText: 'Get Code'
});

const emit = defineEmits<Emits>();

// 响应式数据
const isCounting = ref(false);
const remainingTime = ref(props.duration);
let timer: NodeJS.Timeout | null = null;

// 计算按钮文本
const buttonText = computed(() => {
  if (isCounting.value) {
    return `${remainingTime.value}s`;
  }
  return remainingTime.value <= 0 ? props.finishedText : props.defaultText;
});

// 存储数据接口
interface CountdownData {
  startTime: number;
  duration: number;
  endTime: number;
}

// 保存倒计时数据到本地存储
const saveCountdownData = () => {
  const now = Date.now();
  const data: CountdownData = {
    startTime: now,
    duration: props.duration,
    endTime: now + remainingTime.value * 1000
  };
  localStorage.setItem(props.storageKey, JSON.stringify(data));
};

// 从本地存储获取倒计时数据
const getCountdownData = (): CountdownData | null => {
  try {
    const data = localStorage.getItem(props.storageKey);
    return data ? JSON.parse(data) : null;
  } catch (error) {
    console.error('Failed to parse countdown data:', error);
    return null;
  }
};

// 清除本地存储数据
const clearCountdownData = () => {
  localStorage.removeItem(props.storageKey);
};

// 初始化倒计时状态
const initCountdown = () => {
  const data = getCountdownData();
  if (!data) return;

  const now = Date.now();
  const timeLeft = Math.ceil((data.endTime - now) / 1000);

  if (timeLeft > 0) {
    // 还在倒计时中
    remainingTime.value = timeLeft;
    startCountdown();
  } else {
    // 倒计时已结束
    clearCountdownData();
    remainingTime.value = props.duration;
  }
};

// 开始倒计时
const startCountdown = () => {
  if (isCounting.value) return;

  isCounting.value = true;
  saveCountdownData();

  timer = setInterval(() => {
    remainingTime.value--;

    // 触发状态变化事件
    emit('countdown-change', isCounting.value, remainingTime.value);

    if (remainingTime.value <= 0) {
      stopCountdown();
    } else {
      // 更新本地存储
      saveCountdownData();
    }
  }, 1000);
};

// 停止倒计时
const stopCountdown = () => {
  if (timer) {
    clearInterval(timer);
    timer = null;
  }

  isCounting.value = false;
  remainingTime.value = props.duration;
  clearCountdownData();

  // 触发状态变化事件
  emit('countdown-change', false, remainingTime.value);
};

// 处理按钮点击
const handleClick = () => {
  if (props.disabled || isCounting.value) return;

  emit('click');
  startCountdown();
};

// 重置倒计时（外部调用）
const reset = () => {
  stopCountdown();
};

// 手动开始倒计时（外部调用）
const start = () => {
  if (!isCounting.value) {
    startCountdown();
  }
};

// 监听 duration 变化
watch(() => props.duration, (newDuration) => {
  if (!isCounting.value) {
    remainingTime.value = newDuration;
  }
});

// 组件挂载时初始化
onMounted(() => {
  initCountdown();
});

// 组件卸载时清理定时器
onUnmounted(() => {
  if (timer) {
    clearInterval(timer);
  }
});

// 暴露方法给父组件
defineExpose({
  reset,
  start,
  isCounting: computed(() => isCounting.value),
  remainingTime: computed(() => remainingTime.value)
});
</script>

<style lang="scss" scoped>
.code-countdown-btn {
  width: auto;
  height: 36px;
  min-width: 70px;
  border: none;
  font-family: Inter;
  font-size: 16px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  outline: none;

}
</style>
