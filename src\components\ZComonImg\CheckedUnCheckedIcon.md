# CheckedUnCheckedIcon 组件优化说明

## 🎯 优化内容

### 1. **代码结构优化**
- 使用 TypeScript 接口定义 Props
- 添加详细的 JSDoc 注释
- 提取常量，提高代码可维护性
- 优化计算属性逻辑

### 2. **类型安全**
- 完整的 TypeScript 类型定义
- Props 接口化
- 常量类型约束

### 3. **向后兼容**
- 保持所有原有 API 不变
- 支持 `isMaya` 属性（兼容旧版本）
- 支持 `type` 属性确定颜色主题

### 4. **用户体验优化**
- 添加选中状态的缩放动画
- 添加悬停效果
- 平滑的过渡动画

## 📋 API 文档

### Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `size` | `number` | `20` | 图标大小（像素） |
| `isChecked` | `boolean` | `false` | 是否选中状态 |
| `type` | `string` | `''` | 类型，用于确定颜色主题 |
| `isMaya` | `boolean` | `false` | 是否为 Maya 类型（兼容旧版本） |

### 颜色主题

- **Maya**: `#00A74C` (绿色)
- **GCash**: `#004CDD` (蓝色)  
- **默认**: `#00A74C` (绿色)

## 🔧 使用示例

### 基础用法

```vue
<template>
  <!-- 默认未选中状态 -->
  <CheckedUnCheckedIcon />
  
  <!-- 选中状态 -->
  <CheckedUnCheckedIcon :isChecked="true" />
  
  <!-- 自定义大小 -->
  <CheckedUnCheckedIcon :size="24" :isChecked="true" />
</template>
```

### 提现类型使用

```vue
<template>
  <!-- GCash 类型 -->
  <CheckedUnCheckedIcon 
    :type="WITHDRAW_TYPE.G_CASH" 
    :isChecked="selectedType === 'Gcash'" 
  />
  
  <!-- Maya 类型 -->
  <CheckedUnCheckedIcon 
    :type="WITHDRAW_TYPE.MAYA" 
    :isChecked="selectedType === 'Maya'" 
  />
</template>
```

### 兼容旧版本 API

```vue
<template>
  <!-- 使用 isMaya 属性（兼容旧版本） -->
  <CheckedUnCheckedIcon 
    :isMaya="false" 
    :isChecked="checkedAccountId === item.account_id" 
  />
  
  <CheckedUnCheckedIcon 
    :isMaya="true" 
    :isChecked="checkedAccountId === item.account_id" 
  />
</template>
```

## 🎨 样式特性

### 动画效果
- **选中状态**: 轻微放大效果 (`scale(1.05)`)
- **悬停效果**: 阴影效果
- **过渡动画**: 0.2秒平滑过渡

### 自定义样式
```scss
.checked-unchecked-icon {
  // 可以通过 CSS 变量自定义样式
  --icon-bg-color: #fff;
  --icon-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
```

## 🔄 迁移指南

### 从旧版本迁移

**无需修改代码**，所有现有用法保持不变：

```vue
<!-- 这些用法都继续有效 -->
<CheckedUnCheckedIcon :isMaya="false" :isChecked="true" />
<CheckedUnCheckedIcon :type="item.name" :isChecked="selected" />
<CheckedUnCheckedIcon :size="24" :isChecked="checked" />
```

### 推荐的新用法

```vue
<!-- 推荐使用 type 属性替代 isMaya -->
<CheckedUnCheckedIcon 
  :type="WITHDRAW_TYPE.MAYA" 
  :isChecked="isSelected" 
/>

<CheckedUnCheckedIcon 
  :type="WITHDRAW_TYPE.G_CASH" 
  :isChecked="isSelected" 
/>
```

## 🐛 修复的问题

1. **代码重复**: 消除了重复的条件判断逻辑
2. **类型安全**: 添加了完整的 TypeScript 类型支持
3. **可维护性**: 提取常量，便于后续维护
4. **性能优化**: 优化了计算属性的逻辑
5. **用户体验**: 添加了平滑的动画效果

## 📊 性能对比

| 指标 | 优化前 | 优化后 | 改进 |
|------|--------|--------|------|
| 代码行数 | 66 行 | 92 行 | +40% (增加注释和类型) |
| 类型安全 | ❌ | ✅ | 完整支持 |
| 可维护性 | ⭐⭐ | ⭐⭐⭐⭐⭐ | 显著提升 |
| 用户体验 | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | 添加动画效果 |

现在这个组件具有更好的类型安全性、可维护性和用户体验！
