<template>
  <div v-if="showDownloadGuide" class="download-guide-container">
    <div class="download-guide-content">
      <div class="close-btn" @click="handleClose">
        <ZIcon type="icon-guanbi1" color="#fff" :size="16" />
      </div>
      <div class="app-info">
        <img :src="downloadConfig.app_icon || defaultAppIcon" alt="App Icon" class="app-icon" />
        <div class="app-details">
          {{
            downloadConfig.description ||
            "Download the app, log in, and get ₱100 as a welcome bonus!"
          }}
        </div>
      </div>
      <div class="download-btn" @click="handleDownload">
        {{ downloadConfig.button_text || "Download" }}
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from "vue";
import { useAutoPopMgrStore } from "@/stores/autoPopMgr";
import { MobileWindowManager } from "@/utils/managers/MobileWindowManager";

// 默认应用图标 - 使用favicon
const defaultAppIcon = "/favicon.ico";

// Store引用
const autoPopMgrStore = useAutoPopMgrStore();
const { downloadGuideConfig } = storeToRefs(autoPopMgrStore);

// 本地状态
const showDownloadGuide = ref(false);

// 计算属性：下载配置
const downloadConfig = computed(() => {
  return downloadGuideConfig.value || {};
});

// 检查是否应该显示下载引导
const shouldShowDownloadGuide = () => {
  // 只根据后台配置的 is_enabled 字段控制显示
  return downloadConfig.value?.is_enabled === true;
};

// 处理关闭
const handleClose = () => {
  showDownloadGuide.value = false;
};

// 处理下载
const handleDownload = () => {
  const downloadUrl = downloadConfig.value?.download_url;

  if (downloadUrl) {
    // 使用MobileWindowManager处理下载链接
    const success = MobileWindowManager.navigateToUrl(downloadUrl);
    if (!success) {
      console.error("Failed to open download URL:", downloadUrl);
    }
  } else {
    console.warn("Download URL not configured");
  }

  // 下载后关闭提示
  showDownloadGuide.value = false;
};

// 初始化显示逻辑
const initDownloadGuide = async () => {
  // 先获取配置
  await autoPopMgrStore.getDownloadGuideConfig();

  // 如果后台配置启用，则显示
  if (shouldShowDownloadGuide()) {
    showDownloadGuide.value = true;
  }
};

// 组件挂载时初始化
onMounted(() => {
  initDownloadGuide();
});

// 暴露方法供外部调用
defineExpose({
  show: () => {
    showDownloadGuide.value = true;
  },
  hide: () => {
    showDownloadGuide.value = false;
  },
});
</script>

<style lang="scss" scoped>
.download-guide-container {
  position: fixed;
  bottom: 80px; /* 避免遮挡底部导航栏，TabBar高度54px + 安全区域 */
  left: 12px;
  right: 12px;
  z-index: 999; /* 低于弹窗组件的z-index */
  background: #ac1140;
  padding: 8px 12px;
  border-radius: 12px; /* 添加圆角 */
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  animation: slideUp 0.3s ease-out;
  font-family: "Inter";
}

.download-guide-content {
  display: flex;
  align-items: center;
  gap: 12px;
  position: relative;
}

.close-btn {
  position: absolute;
  top: -10px;
  right: -12px;
  width: 16px;
  height: 16px;
}

.app-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.app-icon {
  width: 36px;
  height: 36px;
  border-radius: 12px;
  object-fit: cover;
}

.app-details {
  flex: 1;
  color: white;
  font-size: 12px;
  font-weight: 500;
}

.download-btn {
  background: #fff;
  color: #ac1140;
  width: 92px;
  height: 36px;
  line-height: 36px;
  text-align: center;
  border-radius: 100px;
  font-size: 14px;
  font-weight: 700;
  white-space: nowrap;
  transition: all 0.2s ease;

  &:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

// 响应式适配
@media (max-width: 375px) {
  .download-guide-container {
    padding: 10px 12px;
  }

  .app-icon {
    width: 40px;
    height: 40px;
  }

  .app-description {
    font-size: 11px;
  }

  .download-btn {
    padding: 8px 16px;
    font-size: 13px;
  }
}
</style>
