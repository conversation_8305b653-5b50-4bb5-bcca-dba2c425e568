// store/global.ts
import { defineStore } from "pinia";
import { E_CHANEL_TYPE } from "@/utils/config/GlobalConstant";
import { backhall, getGameConfig } from "@/api/games";
import { removeLocalStorage } from "@/utils/core/Storage";
import router from "@/router";
import { KycMgr } from "@/utils/KycMgr";
import type {
  UserInfo,
  PayAccount,
  RegisterAward,
  UnreadMarks,
  LoginInfo,
  GlobalStoreState,
} from "@/types/store";

export const GLOBAL_STORE = "GLOBAL_STORE";

// 初始状态值
const initValue: GlobalStoreState = {
  token: "",
  channel: E_CHANEL_TYPE.WEB,
  userInfo: {},
  payAccount: {},
  registerAward: {
    amount: 0,
    type: -1,
  },
  unreadMarks: {},
  // 余额
  balance: 0,
  // 有未读客服消息
  hasUnreadCustomerMsg: false,
  // 有未读站内消息
  hasUnreadIndexboxMsg: false,
  // 有未读新闻
  hasUnreadNews: false,
  loginConfig: {
    // 1关 0开
    login_facebook: 1,
    login_google: 1,
    login_password: 0,
  },
};

export const useGlobalStore = defineStore("global", {
  state: (): GlobalStoreState => ({
    ...initValue,
  }),

  getters: {
    // 是否小程序端，即 G_CASH、MAYA 端
    isMiniChannel(): boolean {
      return ["gcash", "maya"].includes(this.channel?.toLowerCase());
    },
    isMaya() {
      return this.channel?.toLowerCase() === "maya";
    },
    /**
     * 是否已登录
     */
    isLoggedIn: (state): boolean => !!state.token,
  },

  actions: {
    /**
     * 获取用户余额
     */
    async getBalance(): Promise<number> {
      if (!this.token) {
        console.warn("No token available, cannot fetch balance");
        return 0;
      }
      try {
        const result = await backhall();
        const data = result?.data || result || {};
        this.balance = (data.balance || 0) / 100;
        return this.balance;
      } catch (error) {
        console.error("Failed to get balance:", error);
        // 可以选择抛出错误或设置默认值
        this.balance = 0;
        return 0;
      }
    },

    /**
     * 设置登录信息
     * @param info 登录信息
     */
    setLoginInfo(info: Partial<LoginInfo>): void {
      this.token = info.token || "";
      this.userInfo = info.user_info || {};
      this.unreadMarks = info.unread_marks || {};
      this.registerAward = info.register_award || {};
      this.payAccount = info.pay_account || {};
    },

    /**
     * 设置 token
     * @param token 用户令牌
     */
    setToken(token: string): void {
      this.token = token;
    },

    /**
     * 设置渠道
     * @param channel 渠道类型
     */
    setChannel(channel: E_CHANEL_TYPE): void {
      this.channel = channel;
    },

    /**
     * 更新用户信息
     * @param payload 用户信息更新数据
     */
    updateUserInfo(payload: Partial<UserInfo> = {}): void {
      this.userInfo = { ...this.userInfo, ...payload };
    },

    /**
     * 更新支付账户信息
     * @param payload 支付账户更新数据
     */
    updatePayAccount(payload: Partial<PayAccount>): void {
      this.payAccount = { ...this.payAccount, ...payload };
    },

    /**
     * 更新注册奖励信息
     * @param payload 注册奖励更新数据
     */
    updateRegisterAward(payload: Partial<RegisterAward> = {}): void {
      this.registerAward = { ...this.registerAward, ...payload };
    },

    /**
     * 更新余额
     * @param balance 新余额
     */
    updateBalance(balance: number): void {
      this.balance = balance;
    },
    /**
     * 设置未读新闻状态
     * @param hasUnread 是否有未读新闻
     */
    setUnreadNews(hasUnread: boolean): void {
      this.hasUnreadNews = hasUnread;
    },
    /**
     * 设置未读站内消息状态
     * @param hasUnread 是否有未读站内消息
     */
    setUnreadIndexboxMsg(hasUnread: boolean): void {
      this.hasUnreadIndexboxMsg = hasUnread;
    },

    /**
     * 用户登出
     * @param redirectToLogin 是否重定向到登录页，默认为 true
     */
    loginOut(redirectToLogin: boolean = true): void {
      try {
        // 清除本地缓存
        removeLocalStorage(GLOBAL_STORE);

        // 清除 KYC 数据
        // KycMgr.instance?.clearData();

        // 重置 state
        this.clearStore();

        // 跳转到登录页
        if (redirectToLogin) {
          router.replace("/login");
        }
      } catch (error) {
        console.error("Error during logout:", error);
        // 即使出错也要清除状态
        this.clearStore();
        if (redirectToLogin) {
          router.replace("/login");
        }
      }
    },

    /**
     * 清空 store 数据
     * 重置所有状态到初始值
     */
    clearStore(): void {
      Object.assign(this, { ...initValue });
    },

    /**
     * 安全地获取用户信息字段
     * @param field 字段名
     * @param defaultValue 默认值
     * @returns 字段值或默认值
     */
    getUserInfoField<T = any>(field: string, defaultValue: T): T {
      return this.userInfo[field] ?? defaultValue;
    },
  },
  persist: {
    key: GLOBAL_STORE,
    storage: window.localStorage,
  },
});
