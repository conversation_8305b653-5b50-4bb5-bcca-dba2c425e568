/**
 * 登录相关 Store
 */

import { defineStore } from "pinia";
import { showToast } from "vant";
import router from "@/router";
import { useGlobalStore } from "@/stores/global";
import { GeetestMgr } from "@/utils/GeetestMgr";
import { Md5 } from "@/utils/core/Md5";
import { setLocalStorage, getLocalStorage } from "@/utils/core/Storage";
import { loginManager } from "@/utils/managers/LoginManager";
import { showZLoading, closeZLoading } from "@/utils/ZLoadingAPI";
import { LoginMgr } from "@/utils/ThirdPartLoginMsg";
import { useAutoPopMgrStore } from "@/stores/autoPopMgr";

import type {
  LoginStoreState,
  PasswordLoginPayload,
  CodeLoginPayload,
  GeetestResult,
  LoginType,
  GeetestCallback,
  PasswordLoginParams,
  CodeLoginParams,
} from "@/types/login";

export const useLoginStore = defineStore("login", {
  state: (): LoginStoreState => ({
    userPhone: "",
    currentLoginMode: "code",
    isCodeInputMode: false,
    isPrivacyAgreed: true,
    isPrivacyDialogVisible: false,
    isLoggingIn: false,
  }),

  getters: {
    /**
     * 是否应该显示关闭按钮
     */
    shouldShowCloseButton(): boolean {
      return (
        this.currentLoginMode === "password" ||
        (this.currentLoginMode === "code" && !this.isCodeInputMode)
      );
    },

    /**
     * 是否应该显示返回按钮
     */
    shouldShowBackButton(): boolean {
      return this.isCodeInputMode;
    },

    /**
     * 是否有第三方登录选项
     */
    hasThirdPartyLogin(): boolean {
      const globalStore = useGlobalStore();
      return (
        globalStore.loginConfig.login_facebook === 0 || globalStore.loginConfig.login_google === 0
      );
    },
  },

  actions: {
    /**
     * 初始化登录页面
     */
    initLoginPage() {
      const globalStore = useGlobalStore();

      // 重置状态
      globalStore.loginOut(false);

      // 初始化第三方登录SDK
      this.initThirdPartySDK();

      // 恢复手机号
      const phoneNum = getLocalStorage("phone");
      if (phoneNum) {
        this.userPhone = phoneNum;
      }

      // 恢复登录模式
      const savedLoginMode = getLocalStorage("loginType");
      if (savedLoginMode && (savedLoginMode === "password" || savedLoginMode === "code")) {
        this.currentLoginMode = savedLoginMode;
      }
      // 重置下验证码登录的步骤
      this.isCodeInputMode = false;
    },

    /**
     * 初始化第三方登录SDK
     */
    initThirdPartySDK() {
      const globalStore = useGlobalStore();

      if (globalStore.loginConfig.login_facebook === 0) {
        LoginMgr.instance.facebook_init();
      }
      if (globalStore.loginConfig.login_google === 0) {
        LoginMgr.instance.google_init();
      }
    },

    /**
     * 处理 Geetest 验证
     */
    async handleGeetestVerification(
      loginType: LoginType = "phone_login_code",
      callback: GeetestCallback
    ): Promise<void> {
      // 检查隐私协议
      if (!this.isPrivacyAgreed && ["phone_code_login", "password_login"].includes(loginType)) {
        this.isPrivacyDialogVisible = true;
        return;
      }

      try {
        await GeetestMgr.instance.geetest_device(loginType, async (ret: any) => {
          if (ret) {
            await callback({
              buds: ret?.buds || "64",
              geetest_guard: ret?.geetest_guard || "",
              userInfo: ret?.userInfo || "",
              geetest_captcha: ret?.geetest_captcha || "",
            });
          }
        });
      } catch (error) {
        console.error("Geetest verification failed:", error);
        showToast("Verification failed");
      }
    },

    /**
     * 执行用户登录
     */
    async executePlayerLogin(payload: PasswordLoginParams | CodeLoginParams): Promise<void> {
      try {
        this.isLoggingIn = true;
        showZLoading({ duration: 0, showCloseButton: false });
        await loginManager.executeLogin(payload);
        this.handleCodePageChange(false);
      } catch (error) {
        console.error("Player login failed:", error);
        throw error;
      } finally {
        this.isLoggingIn = false;
        closeZLoading();
      }
    },

    /**
     * 处理密码登录
     */
    async handlePasswordLogin(payload: PasswordLoginPayload): Promise<void> {
      const { password, phone: phoneNum } = payload;

      if (!password) {
        throw new Error("Password is required");
      }

      return new Promise((resolve, reject) => {
        this.handleGeetestVerification("password_login", async (geetestResult: GeetestResult) => {
          try {
            const loginParams: PasswordLoginParams = {
              login_type: "phone",
              phone: phoneNum || this.userPhone,
              password: Md5.hashStr(password).toString(),
              ...geetestResult,
            };
            await this.executePlayerLogin(loginParams);
            resolve();
          } catch (error) {
            reject(error);
          }
        });
      });
    },

    /**
     * 处理验证码登录
     */
    async handleCodeLogin(payload: CodeLoginPayload): Promise<void> {
      const { verCode, phone: phoneNum } = payload;

      return new Promise((resolve, reject) => {
        this.handleGeetestVerification("phone_code_login", async (geetestResult: GeetestResult) => {
          try {
            const loginParams: CodeLoginParams = {
              login_type: "phone",
              phone: phoneNum || this.userPhone,
              verifyCode: verCode,
              ...geetestResult,
            };
            await this.executePlayerLogin(loginParams);
            resolve();
          } catch (error) {
            reject(error);
          }
        });
      });
    },

    /**
     * 处理Google登录
     */
    tapGoogleLogin() {
      LoginMgr.instance.google_login();
    },

    async handleGoogleLogin(idToken?) {
      const params = {
        googleToken: idToken,
        login_type: "google",
      };
      await this.executePlayerLogin(params);
    },

    /**
     * 处理Facebook登录
     */
    tapFacebookLogin() {
      LoginMgr.instance.facebook_login();
    },
    async handleFacebookLogin(accessToken?, userID?) {
      const params = {
        accessToken: accessToken,
        faceUserId: userID,
        login_type: "facebook",
      };
      await this.executePlayerLogin(params);
    },

    /**
     * 切换到验证码登录
     */
    switchToCodeLogin(phoneNum: string) {
      this.userPhone = phoneNum;
      this.currentLoginMode = "code";
      setLocalStorage("loginType", "code");
    },

    /**
     * 切换到密码登录
     */
    switchToPasswordLogin(phoneNum: string) {
      this.userPhone = phoneNum;
      this.currentLoginMode = "password";
      setLocalStorage("loginType", "password");
    },

    /**
     * 处理验证码页面变化
     */
    handleCodePageChange(showCodeInput: boolean) {
      this.isCodeInputMode = showCodeInput;
    },

    /**
     * 处理返回操作
     */
    handleGoBack() {
      this.isCodeInputMode = false;
    },

    /**
     * 处理关闭页面
     */
    handleClosePage() {
      router.replace("/home");
    },

    /**
     * 处理页面导航
     */
    handleNavigateToPage(path: string) {
      router.push(path);
    },

    /**
     * 处理隐私协议状态变化
     */
    handleAgreementChange(accepted: boolean) {
      this.isPrivacyAgreed = accepted;
    },

    /**
     * 处理隐私政策确认
     */
    handlePrivacyConfirm() {
      this.isPrivacyAgreed = true;
      this.isPrivacyDialogVisible = false;
    },

    /**
     * 重置登录状态
     */
    resetLoginState() {
      this.isCodeInputMode = false;
      this.isPrivacyDialogVisible = false;
      this.isLoggingIn = false;
    },
  },
});
