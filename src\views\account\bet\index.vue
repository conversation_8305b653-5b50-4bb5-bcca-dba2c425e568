<template>
  <ZPage :request="initData" backgroundColor="#F4F8FB" :narBarStyle="{ background: 'transparent' }">
    <div class="mainbox">
      <BetFilters
        v-model:activeBtn="activeBtn"
        v-model:filterProvider="filterProvider"
        :dateOptions="DATE_OPTIONS"
        @filter-change="handleFilterChange"
      />
      <BetTabs
        v-model:activeTab="activeTab"
        v-model:loading="loading"
        :betOrders="betOrders"
        :gamesList="gamesList"
        :tabTitles="TAB_TITLES"
        @load-more="onLoad"
        @item-click="jumpDetail"
        @tab-change="handleTabChange"
      />
    </div>
  </ZPage>
</template>

<script setup lang="ts">
defineOptions({ name: "BetOrders" });

import BetFilters from "./components/BetFilters.vue";
import BetTabs from "./components/BetTabs.vue";
import { useBetData, type FilterProvider, type DateOption } from "./composables/useBetData";
import { ref } from "vue";
import { useRouter } from "vue-router";
import { useGameStore } from "@/stores/game";

const gameStore = useGameStore();
const { vaildThirdCompany } = storeToRefs(gameStore);
const router = useRouter();

interface BetData {
  game_id: string | number;
  [key: string]: any;
}

// Use the composable for data management
const {
  loading,
  gamesList,
  activeTab,
  betOrders,
  getRightText,
  getRightStyle,
  initData: initBetData,
  onLoad: loadMoreData,
} = useBetData();

// Configuration constants
const DATE_OPTIONS: DateOption[] = [
  { name: "Today", value: 1 },
  { name: "Yesterday", value: 2 },
  { name: "Last 3 days", value: 3 },
  { name: "Last 7 days", value: 7 },
];

const TAB_TITLES = {
  3: "Settled",
  1: "Unsettled",
  2: "Cancel",
  promos: "Promos",
  Settled: "3",
  Unsettled: "1",
  Cancel: "2",
  Promos: "promos",
};

// Local state for filters
const selectedProviders = ref<string[]>(["all"]);
const filterProvider = ref<FilterProvider[]>([]);
const activeBtn = ref<DateOption>({ name: "Last 3 days", value: 3 });
const dialogVisible = ref(false);
const visible = ref(false);
const selectedOption = ref(3);

// 提取工具函数
const getTabKey = (key: string | number) => {
  return TAB_TITLES[key] || key;
};

// Wrapper function for the composable's initData
const initData = async (data = {}) => {
  await initBetData(filterProvider.value, activeBtn.value, data);
};

// Wrapper function for load more
const onLoad = async (page: number, status: string) => {
  await loadMoreData(page, status, filterProvider.value, activeBtn.value);
};

// Add new handler functions for the components
const handleFilterChange = () => {
  initData();
};

const handleTabChange = (tab: string) => {
  console.log("Tab changed to:", tab);
};

const handleChange1 = (checkeds: string[]) => {
  const len = checkeds.length;
  if (len <= 0 || checkeds[len - 1] === "all") {
    selectedProviders.value = ["all"];
    return;
  } else {
    const newCheckeds = checkeds.filter((v) => v !== "all");
    selectedProviders.value = [...newCheckeds];
  }
};

const handleConfirm = (type: string, val: any) => {
  if (type === "date") {
    const selectedOption = DATE_OPTIONS.find((d) => d.value === selectedOption.value);
    if (selectedOption) {
      activeBtn.value = selectedOption;
    }
  } else {
    filterProvider.value = vaildThirdCompany.value
      .filter((d: any) => val.includes(d.id))
      .map((d: any) => ({
        id: d.id,
        provider: d.provider || d.short_name || "Unknown",
      }));
  }
  initData();
  dialogVisible.value = false;
};

const handleCancel = (type: string) => {
  if (type === "date") {
    dialogVisible.value = false;
  } else {
    filterProvider.value = [];
  }
};
const jumpDetail = (data: BetData) => {
  router.push({
    path: "/account/bet-detail",
    query: {
      data: JSON.stringify(data),
      gameInfo: JSON.stringify(gamesList.value[data.game_id]),
    },
  });
};
</script>

<style lang="scss" scoped>
.custom-content {
  padding: 0 !important;
}

.mainbox {
  position: relative;
}
</style>
