<template>
  <div class="header-filter">
    <button 
      @click="$emit('filter-click')" 
      :class="{ active: status !== 'All' }" 
      data-key="Status"
    >
      <span>{{ status }}</span>
      <span 
        v-if="status === 'All'" 
        class="iconfont icon-xiangxia"
      ></span>
      <span 
        v-else 
        @click.stop="$emit('clear-filter', $event)" 
        class="iconfont icon-close"
      ></span>
    </button>
  </div>
</template>

<script setup lang="ts">
interface Props {
  status: string;
}

defineProps<Props>();

defineEmits<{
  'filter-click': [];
  'clear-filter': [event: Event];
}>();
</script>

<style lang="scss" scoped>
.header-filter {
  position: absolute;
  top: 45px;
  left: 0;
  width: 100%;
  display: flex;
  gap: 8px;
  padding: 12px;
  z-index: 3;

  > button {
    background-color: #fff;
    display: flex;
    align-items: center;
    gap: 4px;
    color: #000;
    font-size: 13px;
    font-style: normal;
    font-weight: 500;
    height: 28px;
    padding: 0 14px;
    justify-content: center;
    border-radius: 30px;

    > span:last-child {
      color: #999999;
    }

    &.active {
      > span:first-child {
        color: #ac1140;
      }
    }
  }
}
</style>
