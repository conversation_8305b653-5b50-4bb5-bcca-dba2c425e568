<template>
  <ZPage :narBarStyle="{ background: '#000', color: '#fff' }">
    <template #right>
      <van-icon name="question-o" @click="toIntroduction" />
    </template>
    <div class="page-vip">
      <div class="vip-detail" :class="{ noVip: !isVip }">
        <div class="vip-header">
          <div class="vip-header-logo">
            <span class="vip-logo"></span>
            <div>
              <div class="vip-text">{{ isVip ? "VIP" : "Standard Player" }}</div>
              <span class="vip-duration">{{ tarPeriod.join(" ~ ") }}</span>
            </div>
          </div>
          <div class="vip-header-info">
            <span class="bet-label">Bet :</span>
            <span class="bet-value">{{ Number(rebateProgressData.process.score).toFixed(0) }} /
              {{ Number(rebateProgressData.process.bet_line).toFixed(0) }}</span>
          </div>
          <div class="vip-progress-bar">
            <div class="vip-progress-inner" :style="{ width: `calc(${rebateProgressData.progressWidth}% + 2px)` }">
            </div>
            <div class="vip-progress-tag"></div>
          </div>
          <div class="vip-refresh-tip">
            ({{ isVip ? "Keep your VIP rank" : "Level Up" }}) Next refresh time
            {{ nextPeriodStart }}
          </div>
        </div>
        <div class="vip-benefits">
          <div class="benefits-title">
            <span class="benefits-title-left">
              <span class="track"></span>
              <span class="block"></span>
            </span>
            VIP Benefits
            <span class="benefits-title-right">
              <span class="block"></span>
              <span class="track"></span>
            </span>
          </div>
          <div class="benefits-list">
            <div class="benefit-item">
              <span class="benefit-icon">
                <span class="iconfont icon-tongban"></span>
              </span>
              <div class="benefit-desc">
                Highest<br />
                cashback {{ rebateProgressData.conf }}%
              </div>
              <!-- TODO 跳转 Vip反水详情页 promos页面详情 -->
              <button class="benefit-btn">Detail</button>
            </div>
            <div class="benefit-item">
              <span class="benefit-icon">
                <span class="iconfont icon-kefu2"></span>
              </span>
              <div class="benefit-desc">
                Dedicated<br />
                Support Channels
              </div>
              <button @click="handleCustomerService" class="benefit-btn">Customer Service</button>
            </div>
            <div class="benefit-item">
              <span class="benefit-icon">
                <span class="iconfont icon-qianbao"></span>
              </span>
              <div class="benefit-desc">Priority Withdrawals</div>
              <!-- TODO -->
              <button class="benefit-btn">Withdrawal</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ZPage>
</template>

<script setup>
import { ref } from "vue";
import { useRouter } from "vue-router";
import { useGlobalStore } from "@/stores/global";
import { serviceMgr, ServiceType } from "@/utils/ServiceMgr";
import { getCurrentHalfMonthPeriod, getNextHalfMonthStart } from "@/utils/core/tools";
import { rebateProgress, rebateConf } from "@/api/user";

const router = useRouter();
const globalStore = useGlobalStore();

const isVip = ref(globalStore.userInfo.is_vip == 1);
const tarPeriod = ref("");
const nextPeriodStart = ref("");
const rebateProgressData = ref({ process: { bet_line: "10000", score: "0.00" }, conf: "0.00" }); // 进度条、反水比例

// 获取进度条数值与反水比例
const getData = async () => {
  try {
    const confRes = await rebateConf();
    const progressRes = await rebateProgress();
    rebateProgressData.value = {
      process: progressRes,
      // 进度条宽度计算
      progressWidth: (progressRes.score / progressRes.bet_line) * 100,
      conf: confRes.config?.[0]?.[1]?.rate || "--",
    };
  } catch (error) {
    console.error("Error fetching rebate progress:", error);
  }
};

onBeforeMount(() => {
  const vipReaded = localStorage.getItem("vipReaded");
  // 判断是否阅读过介绍页，自动跳转Vip介绍页
  if (!vipReaded) {
    toIntroduction();
    return;
  }
  getData();
  // 初始化时设置当前半月区间
  tarPeriod.value = getCurrentHalfMonthPeriod();
  // 设置下一个半月区间起始时间
  nextPeriodStart.value = getNextHalfMonthStart();
});

const toIntroduction = () => {
  router.push("/account/vip-introduction");
};

// 客服
const handleCustomerService = () => {
  serviceMgr.instance.openChat(ServiceType.Vip);
};
</script>

<style lang="scss" scoped>
.page-vip {
  background: #291403;
  min-height: 100%;
  color: #fff;
}

.vip-detail {
  padding: 16px 0 0 0;
  background-color: #291403;
  height: 100vh;

  .vip-header {
    margin: 0 auto;
    width: 90%;
    border-radius: 16px;
    background: linear-gradient(to right, #fff9e8 10%, #ffdb9c 80%);
    color: #a05b00;
    padding: 12px 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

    .vip-header-logo {
      display: flex;
      align-items: center;
      gap: 16px;

      .vip-logo {
        width: 88px;
        height: 88px;
        background: url("@/assets/images/account/coin_img.png") no-repeat;
        background-size: 134.867vw 70.867vw;
        background-position: left -2vw top -42.8vw;
        display: inline-block;
      }

      .vip-text {
        font-size: 36px;
        font-weight: 700;
        font-family: Arial, Helvetica, sans-serif;
      }

      .vip-duration {
        font-size: 18px;
        font-weight: 700;
        font-family: Arial, Helvetica, sans-serif;
      }
    }

    .vip-header-info {
      margin: 14px 0 8px 0;
      font-size: 16px;

      .bet-label {
        color: #a05b00;
        font-weight: 600;
      }

      .bet-value {
        color: #ff9f19;
        font-weight: 700;
        margin-left: 6px;
      }
    }

    .vip-progress-bar {
      display: flex;
      width: 100%;
      height: 4px;
      background: #fffbf4;
      border-radius: 4px;
      margin-bottom: 8px;
      justify-content: flex-start;
      align-items: center;

      .vip-progress-inner {
        height: 100%;
        background: #ff9f19;
        border-radius: 4px 0 0 4px;
        transition: width 0.3s;
      }

      .vip-progress-tag {
        width: 10px;
        height: 10px;
        background-color: #fff;
        border: 1px solid rgba(251, 200, 125, 1);
        border-radius: 50%;
        position: relative;
        left: -2px;
      }
    }

    .vip-refresh-tip {
      font-size: 12px;
      color: #a05b00;
      margin-top: 16px;
    }
  }

  .vip-benefits {
    margin: 24px auto 0 auto;
    width: 90%;

    .benefits-title {
      display: flex;
      justify-content: center;
      align-items: center;
      text-align: center;
      font-size: 18px;
      font-weight: 600;
      color: #fff7e1;
      margin-bottom: 20px;
      letter-spacing: 1px;
      gap: 20px;

      .benefits-title-left,
      .benefits-title-right {
        position: relative;
        display: flex;
        justify-content: right;
        align-items: center;

        .track {
          display: inline-block;
          width: 60px;
          height: 2px;
          text-align: right;
          background: linear-gradient(to right, #291403 0%, #fff7e1 100%);
        }

        .block {
          position: absolute;
          display: inline-block;
          width: 6px;
          height: 6px;
          background: #fff7e1;
          transform: rotate(45deg);
        }
      }

      .benefits-title-right {
        justify-content: left;

        .track {
          background: linear-gradient(to left, #291403 0%, #fff7e1 100%);
        }
      }
    }

    .benefits-list {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
      justify-content: space-between;

      .benefit-item {
        background: #442c18;
        border-radius: 16px;
        flex: 1 1 45%;
        min-width: 150px;
        max-width: 48%;
        padding: 20px 12px 16px 12px;
        display: flex;
        flex-direction: column;
        align-items: center;

        .benefit-icon {
          background-color: #593e27;
          border-radius: 50%;
          width: 48px;
          height: 48px;
          text-align: center;
          line-height: 48px;
          margin-bottom: 10px;
        }

        .iconfont {
          background: linear-gradient(to right, #fff7e1, #fcd898);
          -webkit-background-clip: text;
          background-clip: text;
          -webkit-text-fill-color: transparent;
          color: transparent;
          font-size: 28px;
          border-radius: 50%;
        }

        .benefit-desc {
          color: #fff7e1;
          font-size: 14px;
          margin-bottom: 12px;
          text-align: center;
        }

        .benefit-btn {
          background: linear-gradient(90deg, #fff9e8 0%, #ffdb9c 70%);
          color: #4c3312;
          border: none;
          border-radius: 999px;
          min-width: 145px;
          padding: 8px;
          font-size: 12px;
          font-weight: 600;
          cursor: pointer;
        }
      }
    }
  }

  &.noVip {
    .vip-header .vip-header-logo .vip-text {
      font-size: 25px;
    }

    .benefits-list {
      position: relative;
      overflow: hidden;

      &::before {
        display: block;
        content: "";
        width: 100%;
        height: 400px;
        position: absolute;
        top: 0;
        left: 0;
        background-color: rgba(41, 20, 3, 0.4);
      }
    }
  }
}
</style>
