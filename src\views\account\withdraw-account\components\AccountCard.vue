<!-- 选择、编辑 -->
<template>
  <div
    class="card-wrap"
    :style="{ background: cardInfo.backgroundColor }"
    @click="() => handleClickCard(item)"
  >
    <div class="card-header">
      <div class="left">
        <div class="card-logo">
          <WithdrawTypeIcon :type="cardInfo.name" />
        </div>
        <span class="card-name">{{ cardInfo.name }}</span>
      </div>
      <div class="right">
        <template v-if="status === Status.CHECK">
          <CheckedUnCheckedIcon
            :type="cardInfo.name"
            :isChecked="checkedAccountId === item.account_id"
          >
          </CheckedUnCheckedIcon>
        </template>
        <ZIcon type="icon-bianji1" v-if="status === Status.EDIT" color="#fff"> </ZIcon>
      </div>
    </div>
    <div class="card-footer">
      <span class="card-num">{{ formatCardNumber(item.account_no) }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps } from "vue";
import { Status, Item } from "./type";
import { formatCardNumber } from "@/utils/core/tools";

const emits = defineEmits(["click"]);

const props = defineProps({
  // 参数透传
  item: {
    type: Object as () => Item,
    required: true,
    default: () => ({}),
  },
  // 状态
  status: {
    type: String as () => Status,
    required: false,
    default: Status.VIEW,
    validator: (value: string) => {
      // 验证值是否在枚举范围内
      return Object.values(Status).includes(value as Status);
    },
  },
  // 选中的 account_id
  checkedAccountId: {
    type: String,
    required: false,
    default: "",
  },
});

const cardInfo = computed(() => {
  if (props.item.type === 12) {
    return { name: "Gcash", backgroundColor: "#4086f4" };
  }
  return { name: "Maya", backgroundColor: "#01d46a" };
});

const handleClickCard = (item) => {
  if (props.status === Status.VIEW) return;
  emits("click", item);
};
</script>

<style scoped lang="scss">
.card-wrap {
  width: 100%;
  height: 160px;
  border-radius: 28px;
  color: #fff;
  padding: 20px;
  box-sizing: border-box;
  /* 可根据需求添加阴影等样式 */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;

  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;

    .left {
      display: flex;
      align-items: center;
      justify-content: center;

      .card-logo {
        margin-right: 10px;
      }

      .card-name {
        color: #fff;
        font-size: 18px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
      }
    }

    .right {
      display: flex;
      align-items: center;
    }
  }

  .card-footer {
    display: flex;
    align-items: center;
    font-size: 20px;
    font-weight: bold;

    .card-num {
      color: #fff;
      text-align: center;
      font-family: D-DIN;
      font-size: 32px;
      font-style: normal;
      font-weight: 700;
      line-height: normal;
    }
  }
}
</style>
