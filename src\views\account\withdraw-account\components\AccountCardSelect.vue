<!-- 提现账户下拉 -->
<template>
  <div
    class="card-wrap"
    :style="{ background: cardInfo.backgroundColor }"
    @click="() => handleClickCard(item)"
  >
    <div class="left">
      <WithdrawTypeIcon :type="cardInfo.name" class="card-logo" />
      <div class="card-info">
        <div class="card-name">{{ item.name }}</div>
        <div class="card-num">{{ formatCardNumber(item.account_no) }}</div>
      </div>
    </div>
    <div class="right" v-if="showSelectIcon">
      <ZIcon type="icon-xiala" color="#fff" :size="24"> </ZIcon>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps } from "vue";
import { formatCardNumber } from "@/utils/core/tools";

const emits = defineEmits(["select"]);

const props = defineProps({
  // 参数透传
  item: {
    type: Object,
    required: true,
    default: () => ({}),
  },
  // 是否显示下拉
  showSelectIcon: {
    type: Boolean,
    required: false,
    default: false,
  },
});

const cardInfo = computed(() => {
  if (props.item.type === 12) {
    return { name: "Gcash", backgroundColor: "#4086f4" };
  }
  return { name: "Maya", backgroundColor: "#01d46a" };
});

const handleClickCard = (item) => {
  emits("select", item);
};
</script>

<style scoped lang="scss">
.card-wrap {
  width: 100%;
  height: 68px;
  border-radius: 20px;
  color: #fff;
  padding: 16px 20px;
  box-sizing: border-box;
  /* 可根据需求添加阴影等样式 */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  justify-content: space-between;

  .left {
    display: flex;
    align-items: center;
    justify-content: center;

    .card-logo {
      margin-right: 10px;
    }

    .card-info {
      .card-name {
        color: #fff;
        font-family: ABeeZee;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        margin-bottom: 2px;
      }

      .card-num {
        color: #fff;
        font-family: D-DIN;
        font-size: 18px;
        font-style: normal;
        font-weight: 700;
        line-height: normal;
      }
    }
  }
}
</style>
