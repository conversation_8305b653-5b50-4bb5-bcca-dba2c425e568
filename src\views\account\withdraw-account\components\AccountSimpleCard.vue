<template>
  <div class="card-wrap" :style="{ background: cardInfo.backgroundColor }" @click="handleCheck">
    <div class="card-logo">
      <WithdrawTypeIcon :type="item.name" :icon="item.icon" />
    </div>
    <span class="card-name">{{ item.name }}</span>
    <div class="check-wrap">
      <template v-if="item.name === WITHDRAW_TYPE.G_CASH">
        <CheckedUnCheckedIcon :type="WITHDRAW_TYPE.G_CASH" :isChecked="checkedType === item.name">
        </CheckedUnCheckedIcon>
      </template>
      <template v-if="item.name === WITHDRAW_TYPE.MAYA">
        <CheckedUnCheckedIcon :type="WITHDRAW_TYPE.MAYA" :isChecked="checkedType === item.name">
        </CheckedUnCheckedIcon>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps } from "vue";
import { WITHDRAW_TYPE } from "@/utils/config/GlobalConstant";
import { Item } from "./type";

const emits = defineEmits(["check"]);

const props = defineProps({
  checkedType: {
    type: String as () => WITHDRAW_TYPE,
    required: false,
    default: WITHDRAW_TYPE.MAYA,
    validator: (value: string) => {
      return Object.values(WITHDRAW_TYPE).includes(value as WITHDRAW_TYPE);
    },
  },
  item: {
    type: Object as () => Item,
    required: false,
    default: () => {},
  },
});
const cardInfo = computed(() =>
  props.item.name === WITHDRAW_TYPE.G_CASH
    ? { backgroundColor: "#4086f4" }
    : { backgroundColor: "#01d46a" }
);

const handleCheck = () => {
  emits("check", props.item);
};
</script>

<style scoped lang="scss">
.card-wrap {
  width: 100%;
  height: 68px;
  border-radius: 20px;
  color: #fff;
  box-sizing: border-box;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  padding: 18px 12px;
  position: relative;

  .card-name {
    margin-left: 8px;
    color: #fff;
    font-size: 18px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
  }

  .check-wrap {
    position: absolute;
    right: 12px;
    bottom: 6px;
  }
}
</style>
