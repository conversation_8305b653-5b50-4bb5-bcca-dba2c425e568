<script setup lang="ts">

import { useKycStore } from '@/stores/kyc'
import { S_INCOME_ENUM, } from '@/views/kyc/CONSTANT'

const kycStore = useKycStore()

const { detailFormData, isSameCurrentAddress, isGovemmentOfficial } = storeToRefs(kycStore)

console.log('isSameCurrentAddress', isSameCurrentAddress.value)

const updateStore = (field: keyof typeof detailFormData.value, value: any) => {
  kycStore.updateDetailFormData(field, value);
};
</script>
<template>
  <div class="form">
    <div class="address_tip">
      <ZIcon color="#FF936F" type="icon-warn" :size="16"></ZIcon>
      The address must contain complete information
      (including room number/apartment number)
      For example: Hyatt Lake, 1808, 7364 Jermain Way,
      Al Barsha
    </div>
    <div class="form-item">
      <label for="place_of_birth">Place of Birth</label>
      <input v-model="detailFormData.place_of_birth" id="place_of_birth" placeholder="Please enter your place of birth"
        @input="updateStore('place_of_birth', $event.target.value)" />
    </div>
    <div class="form-item">
      <label for="current_address">Current Address</label>
      <input v-model="detailFormData.current_address" id="current_address"
        placeholder="Please enter your current address" @input="updateStore('current_address', $event.target.value)" />
    </div>
    <div class="form-item">
      <label for="permanent_address">Permanent Address</label>
      <input v-model="detailFormData.permanent_address" id="permanent_address"
        placeholder="Please enter your permanent address" @input="updateStore('permanent_address', $event.target.value)"
        :disabled="isSameCurrentAddress" />
      <van-checkbox class="same-address" @change="kycStore.handleSameAddressChecked" v-model="isSameCurrentAddress"
        checked-color="#AC1240" icon-size="16px">Same As Current Address</van-checkbox>
    </div>
    <div class="form-item">
      <label for="work">Nature of Work</label>
      <input v-model="detailFormData.work" id="work" placeholder="Please enter your nature of work"
        @input="updateStore('work', $event.target.value)" />
    </div>
    <div class="form-item">
      <label for="income">Source of Income</label>
      <ZSelect title="Source of Income" :modelValue="detailFormData.income" :selectList="S_INCOME_ENUM"
        @confirm="(e) => kycStore.handleSelectConfirm('income', e)" placeholder="Please select your source of income">
      </ZSelect>
    </div>
    <div class="footer">
      <van-checkbox v-model="isGovemmentOfficial" checked-color="#AC1240" icon-size="16px">Same As
        I am not a govemment official or employee
        connected directly with the operation of the
        Goverment or any of it's agencies, member of the
        Armed Forces of the Philippines, including the
        Army, Navy, Air Force, or the Philippine National
        Police.</van-checkbox>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.footer {
  .van-checkbox {
    align-items: flex-start;

    &:deep(.van-checkbox__label) {
      color: #999;
    }
  }

}

.form {
  .address_tip {
    color: var(--ff-936-f, #FF936F);
    font-family: Inter;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 18px;
    background-color: #FFFAF8;
    padding: 8px 12px;
    display: flex;
    padding: 8px 12px;
    justify-content: space-between;
    align-items: flex-start;
    border-radius: 8px;
    gap: 6px;
    margin-bottom: 10px;
    /* 150% */
  }

  .form-item {
    display: flex;
    flex-direction: column;
    margin-bottom: 16px;
    width: 100%;

    .error {
      color: #FF936F
    }

    .same-address {
      margin-top: 4px;
    }

    label {
      margin-bottom: 8px;
      color: #666;
      font-family: Inter;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }

    input {
      --van-field-border: none;
      /* 移除 Vant Field 默认底部边框 */
      --van-field-padding: 8px 0;
      /* 调整内边距，适配独占一行 */
      background-color: #f7f8fa;
      /* 背景色示例，可根据设计调整 */
      border-radius: 999px;
      display: inline-flex;
      height: 42px;
      box-sizing: border-box;
      padding: 12px 20px;
      align-items: center;
      flex-shrink: 0;
      color: #222;
      font-family: D-DIN;
      font-size: 14px;
      font-style: normal;
      font-weight: 700;
      line-height: normal;
      letter-spacing: -0.3px;

      &::placeholder {
        color: #C0C0C0;

        /* 输入框内默认字体 */
        font-family: Inter;
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
      }
    }

    .dob-wrapper {
      width: 100%;
      display: flex;
      gap: 10px;

      input {
        flex: 1;
        width: 33%;
      }
    }
  }
}
</style>
