<template>
  <div>
    <!-- 验证码登录 手机号-->
    <div v-show="!showCodeInput" class="step1">
      <PhoneInput v-model="phone" @validityChange="onPhoneValidityChange" />
      <ZButton :click="toInCode" :disabled="!phoneIsValid" class="btn"> Register / Log in </ZButton>
      <div @click="checkLoginTypeClick" class="form-redline">
        Login With Password
        <ZIcon type="icon-qianjin" color=""></ZIcon>
      </div>
    </div>
    <!-- 验证码 -->
    <div class="step2" v-show="showCodeInput">
      <div class="form-line">
        A text messsage with a 6-digit code was just sent to
        <div class="phone">{{ maskString(phone + "") }}</div>
      </div>
      <div class="input-wrap">
        <input
          class="input"
          id="verCode"
          v-model="verCode"
          maxlength="6"
          placeholder="Enter the code"
          type="number"
          inputmode="numeric"
        />
        <CodeCountdownButton :storage-key="`login_countdown_${phone}`" @click="getCode" />
      </div>
      <ZButton class="btn" :disabled="!verCode" @click="codeLogin"> Log in </ZButton>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { showToast } from "vant";
import { sendCodeMsg } from "@/api/user";
import { maskString } from "@/utils/core/tools";
import { useRouter } from "vue-router";
import { ref, watch } from "vue";
import PhoneInput from "./PhoneInput.vue";
import CodeCountdownButton from "@/components/CodeCountdownButton.vue";
import { showZLoading, closeZLoading } from "@/utils/ZLoadingAPI";

const router = useRouter();

interface Props {
  phoneValue?: string;
  showCodeInput?: boolean;
  onLogin?: (payload: { verCode: string; phone: string }) => Promise<void>;
  onSwitchMode?: (phone: string) => void;
  onPageChange?: (showCodeInput: boolean) => void;
  geetestHandler?: (type: string, callback: (ret: any) => Promise<void>) => Promise<void>;
}

const props = withDefaults(defineProps<Props>(), {
  phoneValue: "",
  showCodeInput: false,
  onLogin: undefined,
  onSwitchMode: undefined,
  onPageChange: undefined,
  geetestHandler: undefined,
});

const emit = defineEmits<{
  changePage: [value: boolean];
}>();

// 响应式数据
const verCode = ref("");
const phone = ref(props.phoneValue);
const showCodeInput = ref(props.showCodeInput);
const phoneIsValid = ref(false);

// 验证码发送状态
const hasSendMsg = ref(false);

// 监听器
watch(
  () => props.phoneValue,
  (newVal) => {
    phone.value = newVal;
  }
);

watch(
  () => props.showCodeInput,
  (newVal) => {
    showCodeInput.value = newVal;
  }
);

// 事件处理方法
const onPhoneValidityChange = (isValid: boolean) => {
  phoneIsValid.value = isValid;
};

const checkLoginTypeClick = () => {
  props.onSwitchMode?.(phone.value);
};

// 获取验证码
const getCode = async (type = "phone_login_code") => {
  if (!props.geetestHandler) {
    showToast("Geetest function not available");
    return;
  }

  try {
    await props.geetestHandler(type, async (ret: any) => {
      const params = {
        phone: phone.value,
        telephoneCode: "+63",
        type: type === "phone_login_code" ? "1" : "3",
        ...ret,
      };

      showZLoading({ duration: 0, showCloseButton: false });

      try {
        await sendCodeMsg(params);
        hasSendMsg.value = true;
        showToast("Verification code sent successfully");
      } catch (sendError) {
        console.log("sendError", sendError);
        const errorMessage =
          sendError instanceof Error
            ? sendError.message
            : sendError || "Failed to send verification code";
        showToast(errorMessage);
        throw sendError; // 重新抛出错误，让外层catch处理
      } finally {
        closeZLoading(); // 确保无论成功还是失败都关闭loading
      }
    });
  } catch (error) {
    console.log("error", error);
    console.error("Get code error:", error);
    // 这里不需要再次调用closeZLoading，因为内层finally已经处理了
  }
};

const codeLogin = async () => {
  if (!hasSendMsg.value) {
    showToast("Please get the verification code first");
    return;
  }

  if (!verCode.value) {
    showToast("Please enter verification code");
    return;
  }

  try {
    props.onLogin?.({
      verCode: verCode.value,
      phone: phone.value,
    });
  } catch (error) {
    console.error("Code login error:", error);
    // 错误处理由父组件处理
  }
};

// 去发送验证码
const toInCode = () => {
  if (!phoneIsValid.value) {
    showToast("Please enter a valid phone number");
    return;
  }

  showCodeInput.value = true;
  props.onPageChange?.(true);
  emit("changePage", true);
};
</script>

<style lang="scss" scoped>
.step1 {
  .form-redline {
    text-align: center;
    margin-top: 16px;
    color: #666;
    font-family: Inter;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
  }

  .btn {
    margin-top: 28px;
    height: 48px;
  }
}

.step2 {
  .form-line {
    margin-bottom: 16px;
    color: #666;
    font-family: Inter;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;

    .phone {
      color: #222;
      font-family: D-DIN;
      font-size: 18px;
      font-style: normal;
      font-weight: 700;
      line-height: normal;
      margin-top: 10px;
    }
  }

  .btn {
    margin-top: 28px;
    height: 48px;
  }

  /* 获取验证码按钮区域 */
  .input-wrap {
    width: 100%;
    height: 40px;
    border-bottom: 1px solid #ddd;
    display: flex;
    // align-items: center;
    justify-content: space-between;
    // padding-bottom: 10px;

    .input {
      margin-left: 6px;
      flex: 1;
      border: none;
      outline: none;
      background: transparent;
      font-size: 16px;
      font-weight: 600;
      color: #222;
      font-family: D-DIN;
      font-size: 18px;
      font-style: normal;
      font-weight: 700;
      line-height: normal;

      &::placeholder {
        color: #c0c0c0;
        /* 输入框内默认文字 */
        font-family: Inter;
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
      }
    }
  }
}
</style>
