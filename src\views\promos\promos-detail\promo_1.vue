<template>
  <XPage backgroundColor="#000" :navBarStyle="{ background: 'transparent', color: '#fff' }">
    <template #right>
      <van-icon
        @click="() => router.push('/account/transactions/Reward')"
        name="completed-o"
        size="24"
      />
    </template>
    <div class="promo-details">
      <div class="promo-header">
        <img class="promo-bg" src="@/assets/images/promos/promo_1.jpg" alt="Promo Background" />
      </div>
      <div class="promo-content">
        <div class="promo-content-title">GENERAL MECHANICS</div>
        <ul>
          <li>
            Newly registered NUSTAR members will receive an exclusive ₱15 bonus upon registering
            with GCash or Maya.
          </li>
          <li>
            Upon successful registration, the bonus will be automatically credited to eligible
            accounts. This offer is available exclusively to new NUSTAR members.
          </li>
          <li>Withdrawals require 1x turnover of the bonus amount.</li>
        </ul>
        <div class="footer-bar">
          <img src="@/assets/images/promos/promo_foot.png" alt="" class="footer-icon" />
        </div>
        <div class="promo-signup-bar">
          <button class="promo-signup-btn" @click="onSignUp">Sign up</button>
        </div>
      </div>
    </div>
  </XPage>
</template>

<script setup>
import { useRouter } from "vue-router";
const router = useRouter();

const onSignUp = () => {
  // TODO cocos也没做
  alert("Sign up clicked!");
};
</script>

<style lang="scss" scoped>
.promo-details {
  background: #000;
  color: #f5e5c2;
  height: 100vh;
}
.promo-header {
  position: relative;
  text-align: center;
}
.promo-bg {
  width: 100%;
  height: auto;
}
.currency {
  font-size: 0.8em;
  vertical-align: super;
}
.promo-content {
  position: relative;
  top: -100px;
  background: linear-gradient(to bottom, #ffffff00, #000 20%);
  border-radius: 18px;
  padding: 68px 20px 0px 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  ul {
    color: #f5e5c2;
    font-size: 14px;
    line-height: 1.7;
    padding-left: 20px;
    list-style: auto;
    li {
      margin-top: 10px;
    }
  }
  .promo-content-title {
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 18px;
    color: #f5e5c2;
  }
}
.footer-bar {
  //   position: absolute;
  //   bottom: -40px;
  //   left: 0;
  margin-top: 20px;
  width: 100%;
  background: rgba(0, 0, 0, 0.05);
  img {
    width: 100%;
  }
}
.promo-signup-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 24px 24px 0 0;
  background: rgb(74, 64, 55, 0.8);
  z-index: 10;
  text-align: center;
  padding: 16px;
  .promo-signup-btn {
    width: 90%;
    margin: 0 auto;
    background: linear-gradient(90deg, #5d4a36 0%, #b18a5a 100%);
    color: #fff;
    font-size: 16px;
    padding: 8px 16px;
    font-weight: 500;
    border-radius: 40px;
  }
}
</style>
