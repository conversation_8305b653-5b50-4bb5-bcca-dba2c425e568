<template>
  <XPage :navBarStyle="{ backgroundColor: '#FEA100', color: '#333' }">
    <template #left-icon>
      <ZIcon type="icon-fanhui" />
    </template>
    <img src="@/assets/images/promos/promo_12.png" alt="" />
  </XPage>
</template>

<script setup></script>

<style lang="scss" scoped>
.icon-fanhui {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: rgba(7, 7, 7, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
}
</style>
