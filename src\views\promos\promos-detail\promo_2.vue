<template>
  <XPage
    backgroundColor="transparent"
    :navBarStyle="{ background: '#fff', color: '#000', fontSize: '18px' }"
  >
    <template #right>
      <van-icon
        size="24"
        @click="() => router.push('/account/transactions/Reward')"
        name="completed-o"
      />
    </template>
    <div class="promo2-container">
      <img src="@/assets/images/promos/promo_2_head.jpg" alt="" />
      <div class="promo2-content">
        <div class="promo2-card">
          <h2 class="promo2-subtitle">PRIZES</h2>
          <div class="promo2-table">
            <van-row class="promo2-table-head">
              <van-col span="12">First Time Deposit</van-col>
              <van-col span="12">Bonus</van-col>
            </van-row>
            <van-row v-for="row in prizeList" :key="row.deposit">
              <van-col span="12">₱{{ row.deposit.toLocaleString() }}</van-col>
              <van-col span="12">{{ row.bonus }}</van-col>
            </van-row>
          </div>
        </div>
        <div class="promo2-card">
          <h2 class="promo2-subtitle">GENERAL MECHANICS</h2>
          <ol class="promo2-mechanics">
            <li>
              This exclusive offer is available for NUSTAR members making their first deposit with
              us.
            </li>
            <li>
              To Claim Your Bonus:
              <ul>
                <li>Step 1: Navigate to the "Deposit" section in your account.</li>
                <li>Step 2: Enter your desired deposit amount.</li>
                <li>Step 3: Click "Continue" to proceed.</li>
                <li>
                  Step 4: Once completed, a "Congratulations" message will confirm your successful
                  deposit.
                </li>
              </ul>
            </li>
            <li>Withdrawals require 1x turnover of the bonus amount.</li>
            <li>
              Your bonus will be automatically credited to your account, enhancing your balance
              along with your deposit.
            </li>
          </ol>
        </div>
      </div>
      <img class="foot-img" src="@/assets/images/promos/promo_foot.png" alt="" />
      <!-- Footer Button -->
      <footer class="promo2-footer">
        <button class="promo2-btn">Deposit</button>
      </footer>
    </div>
  </XPage>
</template>

<script setup>
import { useRouter } from "vue-router";
const router = useRouter();
const prizeList = [
  { deposit: 10, bonus: 5 },
  { deposit: 50, bonus: 10 },
  { deposit: 100, bonus: 20 },
  { deposit: 500, bonus: 50 },
  { deposit: 1000, bonus: 100 },
  { deposit: 5000, bonus: 100 },
  { deposit: 10000, bonus: 100 },
  { deposit: 50000, bonus: 100 },
];
</script>

<style lang="scss" scoped>
.promo2-container {
  background: #0b3919;
  min-height: 100vh;
  color: #fff;
  padding-bottom: 84px;
  position: relative;
}
.promo2-content {
  margin: -10px auto;
  background: linear-gradient(-140deg, #133a1c 30%, #3a8f74 100%);
  padding: 0 16px 14px;
  text-align: center;
}
.promo2-title {
  font-size: 18px;
  font-weight: bold;
  color: #fcd846;
  margin-bottom: 10px;
  letter-spacing: 1px;
}
.promo2-bonus {
  font-size: 38px;
  font-weight: bold;
  color: #fff;
  margin-bottom: 18px;
  line-height: 1.1;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}
.promo2-card {
  margin-top: 20px;
  border-radius: 20px;
  background: linear-gradient(-90deg, #0e3c20 20%, #185a36 100%);
  padding-bottom: 15px;
  ol {
    padding: 2px 10px 0;
    list-style: auto;
    ul {
      list-style: none;
      li {
        list-style: none;
      }
    }
  }
}
.promo2-subtitle {
  font-size: 14px;
  color: #fff;
  margin: 0 0 18px;
  text-align: left;
  border-radius: 20px;
  padding: 4px 20px;
  background: linear-gradient(90deg, #015701, #cfdba4);
}
.promo2-table {
  margin: 0 16px;
  background: #014701;
  border-radius: 12px;
  margin-bottom: 18px;
  font-size: 14px;
  overflow: hidden;
  border: 1px solid #739259;
  .promo2-table-head {
    background: #002d00;
    color: #f4e5c9;
    font-weight: 700;
  }
  &:deep(.van-col) {
    padding: 4px 0;
    color: #e3ddba;
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 1px solid #739259;
    font-size: 14px;
    &:last-child {
      border-left: 1px solid #739259;
    }
  }
}
.promo2-mechanics {
  text-align: left;
  margin: 0 0 16px 18px;
  font-size: 14px;
  line-height: 1.7;
  li {
    margin-bottom: 10px;
  }
  ul {
    margin-left: 18px;
    margin-top: 6px;
    li {
      margin-bottom: 4px;
      list-style: disc;
    }
  }
}
.foot-img {
  background: #0b3919;
  margin-top: 12px;
}
.promo2-footer {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(87, 112, 104, 0.5);
  border-radius: 20px 20px 0 0;
  padding: 10px 0;
  display: flex;
  justify-content: center;
  z-index: 10;
}
.promo2-btn {
  width: 90%;
  background: linear-gradient(90deg, #013402 20%, #387729 100%);
  color: #fff;
  font-size: 16px;
  font-weight: bold;
  border: none;
  border-radius: 50px;
  padding: 10px 0;
  margin-top: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}
</style>
