<template>
  <XPage backgroundColor="#250F3B">
    <template #right>
      <van-icon @click="goToReward" name="completed-o" size="24" />
    </template>
    <div class="jili-promo-headImg">
      <img src="@/assets/images/promos/promo_3.jpg" alt="" />
    </div>
    <div class="jili-promo">
      <!-- Tabs -->
      <div class="tabs">
        <div :class="['tab', { active: activeTab === TAB_TYPES.TODAY }]" @click="switchTab(TAB_TYPES.TODAY)">
          Betted Today
        </div>
        <div :class="['tab', { active: activeTab === TAB_TYPES.YESTERDAY }]" @click="switchTab(TAB_TYPES.YESTERDAY)">
          Yesterday Cashback
        </div>
      </div>

      <!-- JILI Games Cashback -->
      <div v-if="pageData" class="card">
        <van-row class="card-title">
          <van-col span="16">JILI Games Cashback</van-col>
          <van-col span="8" class="amount">₱{{ getCurrentData.head.cash_back }}</van-col>
        </van-row>
        <van-row class="card-head">
          <van-col span="5"> Provider </van-col>
          <van-col span="15">
            Betted {{ activeTab === TAB_TYPES.TODAY ? "Today" : "Yesterday(Cashback%)" }}</van-col>
          <van-col span="4"> Cashback </van-col>
        </van-row>
        <van-row class="card-body">
          <van-col span="5"> JILI </van-col>
          <van-col span="15">
            ₱{{ getCurrentData.head.total_bet }}{{ activeTab === TAB_TYPES.TODAY ? "" : `(1.0%)` }}
          </van-col>
          <van-col span="4"> ₱{{ getCurrentData.head.cash_back }} </van-col>
        </van-row>
      </div>

      <!-- Cashback by Category -->
      <div v-if="pageData" class="card">
        <van-row class="card-title">
          <van-col span="16">Cashback</van-col>
          <van-col span="8" class="amount">₱{{ getCurrentData.head.cash_back }}</van-col>
        </van-row>
        <van-row class="card-head">
          <van-col span="5">Category</van-col>
          <van-col span="15">Betted
            {{
              activeTab === TAB_TYPES.TODAY ? "Today(valid bet)" : "Yesterday(Cashback%)"
            }}</van-col>
          <van-col span="4">Cashback</van-col>
        </van-row>
        <van-row v-for="item in getCurrentData.types" :key="item.category" class="card-body">
          <van-col span="5">{{ item.category }}</van-col>
          <van-col span="15">₱{{ item.bet_amount }}{{ activeTab === TAB_TYPES.TODAY ? "" : `(0%)` }}
          </van-col>
          <van-col span="4">₱{{ item.cashback }}</van-col>
        </van-row>
      </div>

      <div v-else class="loading-placeholder">Loading data...</div>

      <div class="promotion-title">This promotion will be credited by 3pm daily!</div>
      <div class="promotion-container">
        <div class="benefit-section">
          <h2 class="section-title">Benefit</h2>

          <div class="cashback-tables">
            <van-row class="cashback-table">
              <van-col span="12">Game Provider</van-col>
              <van-col span="12">CashBack %</van-col>
              <van-col span="12">All JILL Games</van-col>
              <van-col span="12">1.0%</van-col>
            </van-row>
            <van-row class="cashback-table">
              <van-col span="12">Game Provider</van-col>
              <van-col span="12">CashBack %</van-col>
              <van-col span="12">Other Games</van-col>
              <van-col span="12">0.6%</van-col>
            </van-row>
          </div>
        </div>
        <div class="mechanics-section">
          <h2 class="section-title">GENERAL MECHANICS</h2>
          <ul class="mechanics-list">
            <li>This promotion is open to both new and existing NUSTAR members.</li>
            <li>
              NUSTAR members will receive bonuses based on their valid bets placed daily from
              00:00:01 AM to 11:59:59 PM, calculated according to the specific percentage for their
              valid bet range.
            </li>
            <li>
              Bonuses will be automatically credited to member's accounts by 3:00 PM the following
              day.
            </li>
            <li>A minimum bonus of <strong>$1</strong> will be awarded.</li>
            <li>No wagering requirements are necessary for withdrawal.</li>
          </ul>
        </div>
      </div>

      <!-- EXAMPLE -->
      <div class="promotion-container">
        <h1 class="section-title">EXAMPLE</h1>
        <van-row class="cashback-table">
          <van-col span="12"><strong>All JILI Games Valid Bet</strong></van-col>
          <van-col span="12"><strong>Other Games Total Valid Bet</strong></van-col>
          <van-col span="12">₱10,000</van-col>
          <van-col span="12">₱10,000</van-col>
          <van-col span="12"><strong>CashBack %</strong></van-col>
          <van-col span="12"><strong>CashBack %</strong></van-col>
          <van-col span="12">1.0%</van-col>
          <van-col span="12">0.6%</van-col>
          <van-col span="12"><strong>Computation</strong></van-col>
          <van-col span="12"><strong>Computation</strong></van-col>
          <van-col span="12">₱10,000 X 1.0% = Bonus</van-col>
          <van-col span="12">₱10,000 X 0.6% = Bonus</van-col>
          <van-col span="12"><strong>Jili Bonus</strong></van-col>
          <van-col span="12"><strong>Other Games Bonus</strong></van-col>
          <van-col span="12">₱100</van-col>
          <van-col span="12">₱60</van-col>
        </van-row>
      </div>
      <div class="footer-bar">
        <img src="@/assets/images/promos/promo_foot.png" alt="" class="footer-icon" />
      </div>

      <!-- Go Bet Button -->
      <div class="go-bet-bar">
        <button @click="goToBet" class="go-bet-btn">Go Bet</button>
      </div>
    </div>
  </XPage>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import { useRouter } from "vue-router";
import { Icon as VanIcon } from "vant";
import { dailyToday, dailyYesterday, cashback } from "@/api/promos";

// 定义选项卡类型
const TAB_TYPES = {
  TODAY: 1,
  YESTERDAY: 2,
} as const;

// 定义数据接口
interface CashbackHead {
  cash_back: string;
  total_bet: string;
}

interface CashbackType {
  category: string;
  bet_amount: string;
  cashback: string;
}

interface PageData {
  [key: number]: {
    head: CashbackHead;
    types: CashbackType[];
  };
}

const router = useRouter();
const activeTab = ref<TAB_TYPES[keyof TAB_TYPES]>(TAB_TYPES.TODAY);
const pageData = ref<PageData | null>(null);
const isLoading = ref(true);
const error = ref<string | null>(null);

// 获取当前选中的数据
const getCurrentData = computed(() => {
  if (!pageData.value) return { head: { cash_back: "0", total_bet: "0" }, types: [] };
  return pageData.value[activeTab.value] || { head: { cash_back: "0", total_bet: "0" }, types: [] };
});

// 切换选项卡
const switchTab = (tab: TAB_TYPES[keyof TAB_TYPES]) => {
  console.log("切换选项卡", tab, pageData.value);
  activeTab.value = tab;
};

// 前往投注页面
const goToBet = () => {
  router.replace(`/game-categories?categoryId=history`);
};

// 前往奖励页面
const goToReward = () => {
  router.push("/account/transactions/Reward");
};

// 初始化数据
const fetchData = async () => {
  try {
    isLoading.value = true;
    error.value = null;

    const [todayCashback, yesterdayCashback, todayData, yesterdayData] = await Promise.all([
      cashback({ type: TAB_TYPES.TODAY }),
      cashback({ type: TAB_TYPES.YESTERDAY }),
      dailyToday(),
      dailyYesterday(),
    ]);

    pageData.value = {
      [TAB_TYPES.TODAY]: { head: todayCashback, types: todayData },
      [TAB_TYPES.YESTERDAY]: { head: yesterdayCashback, types: yesterdayData.data },
    };
  } catch (err: any) {
    console.error("Failed to fetch data:", err);
    error.value = "Failed to load data. Please try again later.";
  } finally {
    isLoading.value = false;
  }
};

onMounted(() => {
  fetchData();
});
</script>

<style lang="scss" scoped>
.jili-promo-headImg {
  width: 100%;
  height: auto;

  img {
    width: 100%;
    height: auto;
  }
}

.jili-promo {
  position: relative;
  top: -100px;
  left: 0;
  background: linear-gradient(to bottom, transparent 0%, var(--secondary-color) 5%);
  color: var(--text-color-white);
  // padding-bottom: 40px;
}

.header {
  display: flex;
  align-items: center;
  padding: 18px 16px 10px 10px;
  background: var(--secondary-color);
  font-size: 20px;
  font-weight: bold;
  position: relative;
}

.header .title {
  flex: 1;
  text-align: center;
  font-size: 20px;
  font-weight: 600;
}

.header .icon-right {
  font-size: 22px;
}

.tabs {
  display: flex;
  margin: 46px 8px 10px;
  justify-content: center;
  gap: 8px;

  .tab {
    padding: 8px;
    flex: 1;
    text-align: center;
    border-radius: 20px;
    background: linear-gradient(to bottom, #33124e, #12021b);
    color: var(--untab-text-color);
    border: 1px solid var(--promo3-border-color);
    font-weight: 500;
    font-size: 16px;

    &.active {
      background: linear-gradient(to bottom, #48196f, #130122);
      border-color: var(--promo3-border-color);
      color: var(--text-color-white);
    }
  }
}

.card {
  background: var(--secondary-color);
  border-radius: 24px;
  margin: 0 12px 18px 12px;
  font-size: 18px;
  font-weight: 600;
  padding-bottom: 10px;
  border: 1px solid var(--promo3-border-color);
  box-shadow: 0 2px 8px rgba(60, 0, 100, 0.08);

  .card-head,
  .card-body {
    font-size: 12px;
    margin: 0 16px;
    line-height: 50px;
    color: var(--accent-color);

    .van-col:last-child {
      text-align: right;
    }
  }

  .card-body {
    color: var(--text-color-white);
    border-top: 1px solid var(--line-color);
  }
}

.card-title {
  position: relative;
  left: -2px;
  width: calc(100% + 4px);
  padding: 8px 16px;
  border: 1px solid var(--promo3-border-color);
  color: var(--text-color-white);
  background: linear-gradient(to right, #290149 50%, #491278);
  border-radius: 40px;

  .amount {
    font-size: 18px;
    color: var(--text-color-white);
    font-weight: 700;
    text-align: right;
  }
}

.promotion-container {
  margin: 0 auto;
  padding: 0 20px;
  font-family: Arial, sans-serif;
}

.promotion-title {
  text-align: center;
  font-size: 16px;
}

.section-title {
  color: var(--accent-color);
  font-size: 16px;
  margin-top: 16px;
}

.cashback-tables {
  width: 100%;
  margin: 10px 0;
  border-radius: 20px;
}

.cashback-table {
  border: 0.5px solid var(--text-color-white);
  line-height: 24px;
  width: 100%;
  border-collapse: collapse;
  margin: 20px 0;
  text-align: center;
  border-radius: 12px;

  &:deep(.van-col) {
    padding: 8px 12px;
    border: 0.5px solid var(--text-color-white);

    &:first-child {
      border-radius: 12px 0 0 0;
    }

    &:nth-child(2) {
      border-radius: 0 12px 0 0;
    }

    &:last-child {
      border-radius: 0 0 12px 0;
    }

    &:nth-last-child(2) {
      border-radius: 0 0 0 12px;
    }
  }
}

.mechanics-list {
  padding-left: 20px;
  list-style: auto;
  margin: 10px 0;
}

.mechanics-list li {
  margin-bottom: 10px;
  line-height: 1.6;
}

.footer-bar {
  // position: absolute;
  // bottom: 0;
  // left: 0;
  width: 100%;
  background: #3e1363;
  padding: 8px 0;
  margin-top: 20px;

  img {
    width: 100%;
  }
}

.go-bet-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 24px 24px 0 0;
  background: rgb(100, 79, 115, 0.7);
  padding: 12px 0 18px 0;
  z-index: 10;
  text-align: center;
}

.go-bet-btn {
  width: 90%;
  margin: 0 auto;
  background: #3e1363;
  color: var(--text-color-white);
  font-size: 16px;
  font-weight: 600;
  border: none;
  border-radius: 24px;
  padding: 10px 0;
  box-shadow: 0 2px 8px rgba(60, 0, 100, 0.08);
  transition: background 0.2s;

  &:hover {
    background: #5a2092;
  }
}

.loading-placeholder {
  text-align: center;
  padding: 20px;
  color: var(--accent-color);
}
</style>
